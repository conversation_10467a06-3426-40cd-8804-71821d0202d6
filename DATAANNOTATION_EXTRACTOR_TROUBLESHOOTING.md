# DataAnnotation Payment Extractor Troubleshooting

## Problem
The browser extension/script to export payment data from DataAnnotation is not appearing in the DataAnnotation window when opened from the app.

## Root Cause Analysis

### Potential Issues Identified

#### 1. **Script Injection Timing**
- Script might be injected before the page is fully loaded
- DataAnnotation uses a Single Page Application (SPA) which loads content dynamically
- DOM elements might not be available when script runs

#### 2. **Content Security Policy (CSP)**
- DataAnnotation website likely has strict CSP that blocks script injection
- External script execution might be blocked by the website's security policies

#### 3. **Webview Permissions**
- Tauri webview might not have the necessary permissions to inject scripts
- Cross-origin restrictions might prevent script execution

#### 4. **Script File Access**
- Script file might not be accessible from the webview context
- File path resolution issues in Tauri environment

## Debugging Steps Implemented

### ✅ Enhanced Logging
Added comprehensive logging to track:
- Script fetching from `/dataAnnotationExtractor.js`
- Script injection into webview
- Script execution and DOM manipulation
- Toolbar creation and placement

### ✅ Visual Indicators
Added temporary blue border flash when script loads to visually confirm execution.

### ✅ Multiple Fallback Timers
- Immediate execution if DOM is ready
- 2-second delay for SPA behavior
- 5-second delay for slow-loading pages

### ✅ Test Command
Created `test_dataannotation_window` command to manually test the functionality.

## Testing Instructions

### Step 1: Test Script Injection System
1. **Run the application**: `npm run tauri dev`
2. **Click the test button**: "🧪 Test DataAnnotation Window" in the UI
3. **Check terminal logs** for script injection messages:
   ```
   🧪 Test command: Creating DataAnnotation window...
   ✅ DataAnnotation test window created
   📝 Script loaded, length: XXXX characters
   ✅ Script injected successfully into test window
   ```

### Step 2: Check DataAnnotation Window
1. **Look for the test window** that opens DataAnnotation payments page
2. **Check browser console** (if accessible) for script execution logs:
   ```
   🎯 Starting Data Annotation Payment Extractor...
   🚀 Data Annotation Payment Extractor initialized
   📍 Current URL: https://app.dataannotation.tech/workers/payments
   ✅ Document already loaded, creating toolbar immediately...
   🔧 createExportToolbar called
   ✨ Creating new export toolbar...
   ✅ Export toolbar successfully added to DOM
   ```
3. **Look for visual indicators**:
   - Temporary blue border flash (2 seconds)
   - Export toolbar in top-right corner with "Export Payment Data" button

### Step 3: Check Regular DataAnnotation Integration
1. **Click "DataAnnotation" in the navbar**
2. **Wait for the window to open**
3. **Check terminal logs** for injection messages
4. **Look for the export toolbar** in the DataAnnotation window

## Expected Behavior

### ✅ If Working Correctly
- Blue border flash appears briefly when page loads
- Export toolbar appears in top-right corner
- Console shows all initialization and creation logs
- Button is clickable and functional

### ❌ If Not Working
- No blue border flash
- No export toolbar visible
- Missing console logs
- Terminal shows script injection errors

## Potential Solutions

### Solution 1: CSP Bypass (Recommended)
DataAnnotation's CSP might be blocking script injection. Try this approach:

```javascript
// Instead of direct script injection, use a different method
// Inject a minimal script that creates the toolbar without eval()
```

### Solution 2: Alternative Injection Method
Use Tauri's different script injection methods:

```rust
// Try using webview.eval() with a different approach
// Or use webview.with_webview() for more direct access
```

### Solution 3: Browser Extension Approach
Create an actual browser extension instead of script injection:

1. **Create a Chrome/Firefox extension**
2. **Package it with the app**
3. **Instruct users to install it manually**

### Solution 4: Manual Copy-Paste Method
Provide users with a bookmarklet or manual script:

1. **Create a bookmarklet** users can save
2. **Provide instructions** for manual execution
3. **Use clipboard API** for data transfer

## Quick Fixes to Try

### Fix 1: Increase Injection Delay
```javascript
// In App.tsx, increase the timeout
setTimeout(async () => {
  // inject script
}, 10000); // Try 10 seconds instead of 5
```

### Fix 2: Add Page Load Detection
```javascript
// Wait for specific elements to appear
const waitForElement = (selector) => {
  return new Promise(resolve => {
    if (document.querySelector(selector)) {
      return resolve(document.querySelector(selector));
    }
    const observer = new MutationObserver(mutations => {
      if (document.querySelector(selector)) {
        resolve(document.querySelector(selector));
        observer.disconnect();
      }
    });
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  });
};
```

### Fix 3: Alternative Toolbar Placement
```javascript
// Try injecting into different DOM locations
// Instead of document.body, try:
// - document.head
// - document.documentElement
// - First available container
```

## Current Status

### ✅ What's Working
- Script file exists and is accessible
- Script injection system is implemented
- Comprehensive logging is in place
- Test command is available
- Enhanced error handling

### ❌ What Needs Investigation
- Whether script actually executes in DataAnnotation context
- CSP restrictions from DataAnnotation website
- Webview permissions and capabilities
- DOM availability timing

### 🔧 Next Steps
1. **Run the test command** to verify basic functionality
2. **Check browser console** in DataAnnotation window
3. **Try alternative injection methods** if CSP is blocking
4. **Consider browser extension approach** as fallback

The infrastructure is in place - the issue is likely related to DataAnnotation's security policies or timing of script execution.
