/**
 * Services Index
 *
 * Central export file for all application services
 * Provides singleton instances and service interfaces
 */

import { TimerService } from './TimerService';
import { TaskService } from './TaskService';
import { StorageService } from './StorageService';

export { TimerService } from './TimerService';
export { TaskService } from './TaskService';
export { StorageService } from './StorageService';

// Service interfaces for dependency injection
export type { ITimerService } from './TimerService';
export type { ITaskService } from './TaskService';
export type { IStorageService } from './StorageService';

// Service factory for getting singleton instances
export class ServiceFactory {
  private static storageService: StorageService;
  private static timerService: TimerService;
  private static taskService: TaskService;

  static getStorageService(): StorageService {
    if (!ServiceFactory.storageService) {
      ServiceFactory.storageService = new StorageService();
    }
    return ServiceFactory.storageService;
  }

  static getTimerService(): TimerService {
    if (!ServiceFactory.timerService) {
      ServiceFactory.timerService = new TimerService(
        ServiceFactory.getStorageService()
      );
    }
    return ServiceFactory.timerService;
  }

  static getTaskService(): TaskService {
    if (!ServiceFactory.taskService) {
      ServiceFactory.taskService = new TaskService(
        ServiceFactory.getStorageService()
      );
    }
    return ServiceFactory.taskService;
  }

  // Reset all services (useful for testing)
  static resetServices(): void {
    ServiceFactory.storageService = undefined as any;
    ServiceFactory.timerService = undefined as any;
    ServiceFactory.taskService = undefined as any;
  }
}
