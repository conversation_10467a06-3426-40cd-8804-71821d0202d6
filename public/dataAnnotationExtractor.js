// Data Annotation Payment Extractor Script
// This script is injected into the Data Annotation payments page to extract payment data

(function() {
  'use strict';

  // Configuration
  const XPATH_TABLE = '/html/body/div[2]/div/div[2]/div/div[2]/div/div[1]/table';
  const EXTRACTION_DELAY = 1000; // Delay between row expansions
  const MAX_RETRIES = 3;

  // Utility functions
  function waitForElement(xpath, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      function check() {
        const element = document.evaluate(
          xpath,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        ).singleNodeValue;
        
        if (element) {
          resolve(element);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Element not found: ${xpath}`));
        } else {
          setTimeout(check, 100);
        }
      }
      
      check();
    });
  }

  function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  function generateId() {
    return `payout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Extract text content safely
  function getTextContent(element) {
    return element ? element.textContent.trim() : '';
  }

  // Parse hierarchical task data from the "Daily Reported Projects & Bonuses" column
  function parseTaskData(cellElement) {
    const tasks = [];

    if (!cellElement) return tasks;

    // First, try to find nested elements (after expansion, there might be nested divs, spans, etc.)
    const nestedElements = cellElement.querySelectorAll('div, span, p, li, tr, td');

    if (nestedElements.length > 0) {
      // Process nested elements that might contain structured data
      for (const element of nestedElements) {
        const elementText = element.textContent.trim();
        if (elementText && elementText !== cellElement.textContent.trim()) {
          // This is a nested element with different content
          const parsedTasks = parseTextForTasks(elementText);
          tasks.push(...parsedTasks);
        }
      }
    }

    // If no nested elements found or they didn't yield results, parse the main text content
    if (tasks.length === 0) {
      const mainText = cellElement.textContent;
      const parsedTasks = parseTextForTasks(mainText);
      tasks.push(...parsedTasks);
    }

    return tasks;
  }

  // Helper function to parse text content for task information
  function parseTextForTasks(text) {
    const tasks = [];

    if (!text || !text.trim()) return tasks;

    // Split by common separators and clean up
    const lines = text.split(/[\n\r]+|(?:\s*[•·▪▫‣⁃]\s*)|(?:\s*[-–—]\s*)/g)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    let currentDate = '';
    let currentTaskName = '';

    for (const line of lines) {
      // Enhanced date matching patterns
      const datePatterns = [
        /(\d{4}-\d{1,2}-\d{1,2})/,           // YYYY-MM-DD or YYYY-M-D
        /(\d{1,2}\/\d{1,2}\/\d{4})/,         // MM/DD/YYYY or M/D/YYYY
        /(\d{1,2}-\d{1,2}-\d{4})/,           // MM-DD-YYYY or M-D-YYYY
        /(\d{1,2}\.\d{1,2}\.\d{4})/,         // MM.DD.YYYY or M.D.YYYY
        /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2},?\s+\d{4}/i, // Month DD, YYYY
        /(\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4})/i   // DD Month YYYY
      ];

      let dateMatch = null;
      for (const pattern of datePatterns) {
        dateMatch = line.match(pattern);
        if (dateMatch) break;
      }

      if (dateMatch) {
        currentDate = dateMatch[1];
        // Extract task name from the same line if present
        const taskNamePart = line.replace(dateMatch[0], '').replace(/[:\-–—]/, '').trim();
        if (taskNamePart && taskNamePart.length > 2) {
          currentTaskName = taskNamePart;
        }
        console.log(`Found date: ${currentDate}, task name: ${currentTaskName}`);
      } else if (currentDate && line.length > 0) {
        // This might be a task name or action
        if (line.includes('→') || line.includes('->') || line.includes(':') ||
            line.toLowerCase().includes('action') || line.toLowerCase().includes('task')) {
          // This looks like a task action
          tasks.push({
            date: currentDate,
            taskName: currentTaskName || 'Unknown Task',
            taskAction: line.replace(/^[:\-–—]\s*/, '') // Remove leading separators
          });
          console.log(`Added task: ${currentDate} - ${currentTaskName} - ${line}`);
        } else if (line.length > 2 && !currentTaskName) {
          // This might be a task name
          currentTaskName = line;
          console.log(`Set task name: ${currentTaskName}`);
        } else if (line.length > 2) {
          // Additional task or continuation
          tasks.push({
            date: currentDate,
            taskName: line,
            taskAction: currentTaskName || 'Task Completion'
          });
          console.log(`Added additional task: ${currentDate} - ${line}`);
        }
      } else if (!currentDate && line.length > 5) {
        // No date found yet, but this might be a task description
        // Use a fallback date
        const fallbackDate = new Date().toISOString().split('T')[0];
        tasks.push({
          date: fallbackDate,
          taskName: line,
          taskAction: 'Payment Entry'
        });
        console.log(`Added fallback task: ${fallbackDate} - ${line}`);
      }
    }

    // If still no structured data found, create a single task entry
    if (tasks.length === 0 && text.trim()) {
      const fallbackDate = new Date().toISOString().split('T')[0];
      tasks.push({
        date: fallbackDate,
        taskName: text.trim().substring(0, 100), // Limit length
        taskAction: 'Payment Entry'
      });
      console.log(`Created fallback task for: ${text.trim().substring(0, 50)}...`);
    }

    return tasks;
  }

  // Expand all collapsible rows to ensure complete data extraction
  async function expandAllRows(table) {
    console.log('Starting sequential row expansion...');

    const tbody = table.querySelector('tbody');
    const tableBody = tbody || table;

    let totalExpandedCount = 0;
    let iterationCount = 0;
    const maxIterations = 10;

    // Get initial row count
    const initialRowCount = tableBody.querySelectorAll('tr').length;
    console.log(`Initial row count: ${initialRowCount}`);

    // Track processed rows to avoid infinite loops
    const processedRows = new Set();
    const processedElements = new WeakSet(); // Track actual DOM elements

    // Keep expanding until no more expansions are possible
    while (iterationCount < maxIterations) {
      iterationCount++;
      console.log(`\n--- Expansion iteration ${iterationCount} ---`);

      let iterationExpandedCount = 0;
      let currentRowIndex = 0;

      // Get current rows (fresh scan each iteration)
      const currentRows = Array.from(tableBody.querySelectorAll('tr'));
      const dataRows = currentRows.filter(row => {
        const cells = row.querySelectorAll('td');
        return cells.length > 0;
      });

      console.log(`Found ${dataRows.length} data rows in iteration ${iterationCount}`);

      // Process each row sequentially
      for (const row of dataRows) {
        currentRowIndex++;

        // Create a unique identifier for this row based on its content and DOM position
        const titleCell = row.querySelector('[data-testid="cell-title"]');
        if (!titleCell) continue;

        const rowText = titleCell.textContent?.trim() || '';

        // Get indentation level to help with unique identification
        const titleDiv = titleCell.querySelector('div.tw-flex');
        let indentationLevel = 0;
        if (titleDiv) {
          const marginClass = titleDiv.className.match(/tw-ml-(\d+)/);
          if (marginClass) {
            indentationLevel = parseInt(marginClass[1]);
          }
        }

        // Create a more stable unique identifier using content + indentation + position in DOM
        // Use the row's position in the current DOM structure to make it unique
        const allRows = Array.from(tableBody.querySelectorAll('tr'));
        const rowPosition = allRows.indexOf(row);
        const rowId = `${rowText}_indent${indentationLevel}_pos${rowPosition}`;

        // Check if this exact DOM element has been processed before
        if (row.hasAttribute('data-expanded') || processedElements.has(row)) {
          console.log(`Row ${currentRowIndex}: Skipping "${rowText}" - already processed (hasAttribute: ${row.hasAttribute('data-expanded')}, inWeakSet: ${processedElements.has(row)})`);
          continue;
        }

        // Look for expandable indicator in the title cell
        const expandableDiv = titleCell.querySelector('div.tw-cursor-pointer');
        if (!expandableDiv) {
          processedRows.add(rowId);
          processedElements.add(row);
          row.setAttribute('data-expanded', 'no-arrow');
          continue;
        }

        // Check if this row has an SVG arrow (expansion indicator)
        const svgArrow = expandableDiv.querySelector('svg');
        if (!svgArrow) {
          processedRows.add(rowId);
          processedElements.add(row);
          row.setAttribute('data-expanded', 'no-arrow');
          continue;
        }

        console.log(`Row ${currentRowIndex}: Found expandable row "${rowText}" (indent: ${indentationLevel})`);

        try {
          // Check if the element is still clickable before clicking
          const isStillClickable = expandableDiv.querySelector('svg') !== null;
          console.log(`Row ${currentRowIndex}: About to click "${rowText}" (still has SVG: ${isStillClickable})`);

          // Click on the expandable div to expand the row
          expandableDiv.click();
          iterationExpandedCount++;
          totalExpandedCount++;

          console.log(`Row ${currentRowIndex}: Successfully clicked to expand "${rowText}"`);

          // Mark this row as processed both in memory and DOM
          processedRows.add(rowId);
          processedElements.add(row);
          row.setAttribute('data-expanded', 'true');

          // Wait for DOM to update after expansion (longer delay for stability)
          await sleep(EXTRACTION_DELAY);

          // Check if new rows appeared after this click
          const newRowCount = tableBody.querySelectorAll('tr').length;
          console.log(`Row ${currentRowIndex}: After expanding "${rowText}", row count is now ${newRowCount}`);

        } catch (error) {
          console.warn(`Row ${currentRowIndex}: Failed to expand "${rowText}":`, error);
          processedRows.add(rowId);
          processedElements.add(row);
          row.setAttribute('data-expanded', 'error');
        }
      }

      console.log(`Iteration ${iterationCount}: Expanded ${iterationExpandedCount} rows`);

      // If no rows were expanded in this iteration, we're done
      if (iterationExpandedCount === 0) {
        console.log(`No more expandable rows found. Expansion complete.`);
        break;
      }

      // If this was the last iteration with expansions, wait a bit longer to ensure all DOM updates complete
      if (iterationCount >= maxIterations - 1) {
        console.log(`Approaching max iterations, waiting extra time for DOM to settle...`);
        await sleep(EXTRACTION_DELAY * 2);
      }

      // Wait for DOM to settle after this iteration
      await sleep(EXTRACTION_DELAY);

      // Check if new rows appeared and log detailed status
      const currentRowCount = tableBody.querySelectorAll('tr').length;
      console.log(`Row count after iteration ${iterationCount}: ${currentRowCount} (was ${initialRowCount})`);

      // Log how many rows still have expandable indicators
      const stillExpandableRows = Array.from(tableBody.querySelectorAll('tr')).filter(row => {
        const titleCell = row.querySelector('[data-testid="cell-title"]');
        if (!titleCell) return false;
        const expandableDiv = titleCell.querySelector('div.tw-cursor-pointer');
        const svgArrow = expandableDiv?.querySelector('svg');
        const isAlreadyProcessed = row.hasAttribute('data-expanded');
        return expandableDiv && svgArrow && !isAlreadyProcessed;
      });

      console.log(`Remaining expandable rows: ${stillExpandableRows.length}`);
      if (stillExpandableRows.length > 0 && stillExpandableRows.length <= 5) {
        // Log details of remaining expandable rows for debugging
        stillExpandableRows.forEach((row, idx) => {
          const titleCell = row.querySelector('[data-testid="cell-title"]');
          const rowText = titleCell?.textContent?.trim() || '';
          const titleDiv = titleCell?.querySelector('div.tw-flex');
          let indentationLevel = 0;
          if (titleDiv) {
            const marginClass = titleDiv.className.match(/tw-ml-(\d+)/);
            if (marginClass) {
              indentationLevel = parseInt(marginClass[1]);
            }
          }
          console.log(`  Remaining ${idx + 1}: "${rowText}" (indent: ${indentationLevel})`);
        });
      }
    }

    console.log(`\n=== Expansion Summary ===`);
    console.log(`Total iterations: ${iterationCount}`);
    console.log(`Total rows expanded: ${totalExpandedCount}`);
    console.log(`Final row count: ${tableBody.querySelectorAll('tr').length}`);
    console.log(`Processed ${processedRows.size} unique rows`);

    return {
      totalExpanded: totalExpandedCount,
      iterations: iterationCount,
      finalRowCount: tableBody.querySelectorAll('tr').length
    };
  }

  // Extract hierarchical task data from expanded table rows

  // Extract payment data from the table
  async function extractPaymentData() {
    try {
      console.log('Starting payment data extraction...');

      // Wait for the table to be present
      const table = await waitForElement(XPATH_TABLE);
      console.log('Table found:', table);
      console.log('Table HTML structure:', table.outerHTML.substring(0, 500) + '...');

      // Take a snapshot of the table before expansion
      const initialRowCount = table.querySelectorAll('tr').length;
      console.log(`Initial table row count: ${initialRowCount}`);

      // Expand all rows first
      const expansionResult = await expandAllRows(table);
      console.log('Expansion result:', expansionResult);

      // Take another snapshot after expansion
      const finalRowCount = table.querySelectorAll('tr').length;
      console.log(`Final table row count after expansion: ${finalRowCount}`);

      // Find table headers to identify column indices
      const headerRow = table.querySelector('thead tr, tr:first-child');
      if (!headerRow) {
        throw new Error('Table header not found');
      }

      const headers = Array.from(headerRow.querySelectorAll('th, td')).map(cell =>
        getTextContent(cell).toLowerCase()
      );

      console.log('Table headers:', headers);

      // Map column names to indices with more flexible matching
      const columnMap = {
        tasks: headers.findIndex(h =>
          h.includes('daily') || h.includes('project') || h.includes('bonus') ||
          h.includes('reported') || h.includes('work') || h.includes('task')
        ),
        amount: headers.findIndex(h =>
          h.includes('amount') || h.includes('payment') || h.includes('pay') ||
          h.includes('$') || h.includes('total')
        ),
        timeReported: headers.findIndex(h =>
          h.includes('time') && (h.includes('report') || h.includes('submit'))
        ),
        timeAgo: headers.findIndex(h =>
          h.includes('time') && h.includes('ago')
        ),
        status: headers.findIndex(h =>
          h.includes('status') || h.includes('state') || h.includes('condition')
        )
      };

      console.log('Column mapping:', columnMap);

      // If we couldn't find the expected columns, try a more generic approach
      if (columnMap.tasks === -1 && columnMap.amount === -1) {
        console.warn('Could not identify expected columns, using positional fallback');
        // Common patterns: first few columns often contain the main data
        if (headers.length >= 2) {
          columnMap.tasks = 0; // First column often contains task info
          columnMap.amount = 1; // Second column often contains amount
          if (headers.length >= 3) columnMap.status = headers.length - 1; // Last column often status
        }
      }

      // Extract data from table rows with hierarchical grouping
      const tbody = table.querySelector('tbody');
      const dataRows = tbody ?
        tbody.querySelectorAll('tr') :
        table.querySelectorAll('tr:not(:first-child)');

      console.log(`Found ${dataRows.length} data rows to extract from`);
      const extractedEntries = [];

      // Process rows to identify the hierarchical structure
      let currentDate = '';
      let currentPaymentGroup = null;
      let currentParentTaskName = ''; // Track the parent task name for hierarchical processing

      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];
        const cells = row.querySelectorAll('td');

        if (cells.length === 0) {
          console.log(`Row ${i}: Skipping - no td cells found`);
          continue;
        }

        console.log(`Row ${i}: Processing row with ${cells.length} cells`);

        try {
          // Get the title cell content (first column)
          const titleCell = cells[columnMap.tasks >= 0 ? columnMap.tasks : 0];
          const titleText = getTextContent(titleCell);

          // Log cell contents for debugging
          const cellContents = Array.from(cells).map((cell, idx) =>
            `Cell ${idx}: "${getTextContent(cell).substring(0, 50)}..."`
          );
          console.log(`Row ${i} cell contents:`, cellContents);

          // Check if this is a date row (contains only a date like "May 28", "May 22")
          const isDateRow = /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}$|^\d{1,2}\/\d{1,2}$|^\d{4}-\d{1,2}-\d{1,2}$/.test(titleText.trim());

          if (isDateRow) {
            console.log(`Row ${i}: Identified as date row: "${titleText}"`);
            currentDate = titleText.trim();
            currentParentTaskName = ''; // Reset parent task name for new date

            // If we have a previous payment group, add it to results
            if (currentPaymentGroup) {
              extractedEntries.push(currentPaymentGroup);
              console.log(`Added payment group with ${currentPaymentGroup.tasks.length} tasks`);
            }

            // Start a new payment group for this date
            currentPaymentGroup = {
              id: generateId(),
              tasks: [],
              amount: '',
              timeReported: '',
              timeAgo: '',
              status: '',
              extractedAt: new Date().toISOString(),
              sourceUrl: window.location.href
            };
            continue;
          }

          // Determine the indentation level to identify hierarchy
          const titleDiv = titleCell.querySelector('div.tw-flex');
          let indentationLevel = 0;
          if (titleDiv) {
            const marginClass = titleDiv.className.match(/tw-ml-(\d+)/);
            if (marginClass) {
              indentationLevel = parseInt(marginClass[1]);
            }
          }

          console.log(`Row ${i}: Indentation level: ${indentationLevel}, Title: "${titleText}"`);

          // If this is a parent task (indentation level 5), update the parent task name
          if (indentationLevel === 5 && titleText && titleText.trim() && !isDateRow) {
            currentParentTaskName = titleText.trim();
            console.log(`Row ${i}: Set parent task name to: "${currentParentTaskName}"`);
          }

          // This is a task/detail row
          const amount = columnMap.amount >= 0 && cells[columnMap.amount] ?
            getTextContent(cells[columnMap.amount]) : '';
          const timeReported = columnMap.timeReported >= 0 && cells[columnMap.timeReported] ?
            getTextContent(cells[columnMap.timeReported]) : '';
          const timeAgo = columnMap.timeAgo >= 0 && cells[columnMap.timeAgo] ?
            getTextContent(cells[columnMap.timeAgo]) : '';
          const status = columnMap.status >= 0 && cells[columnMap.status] ?
            getTextContent(cells[columnMap.status]) : '';

          console.log(`Row ${i} extracted data:`, {
            titleText,
            amount,
            timeReported,
            timeAgo,
            status,
            currentDate,
            indentationLevel,
            currentParentTaskName
          });

          // If this row has payment data (amount, time, status), it's a payment entry
          if (amount || timeReported || status) {
            if (!currentPaymentGroup) {
              // Create a new payment group if we don't have one
              currentPaymentGroup = {
                id: generateId(),
                tasks: [],
                amount: '',
                timeReported: '',
                timeAgo: '',
                status: '',
                extractedAt: new Date().toISOString(),
                sourceUrl: window.location.href
              };
            }

            // Add task information to the current payment group
            if (titleText && titleText.trim()) {
              if (indentationLevel === 10 && currentParentTaskName) {
                // This is a child row (indentation 10) - use parent task name and current text as action
                const taskName = currentParentTaskName;
                const taskAction = titleText.trim();

                currentPaymentGroup.tasks.push({
                  date: currentDate || new Date().toISOString().split('T')[0],
                  taskName: taskName,
                  taskAction: taskAction
                });

                console.log(`Row ${i}: Added task "${taskName}" with action "${taskAction}" to payment group (indentation: ${indentationLevel})`);
              } else if (indentationLevel === 5) {
                // This is a parent row (indentation 5) - only set parent task name, don't create task entry
                console.log(`Row ${i}: Parent row "${titleText}" - not creating task entry, only setting parent name (indentation: ${indentationLevel})`);
              } else {
                // This is a standalone row (indentation 0) - use current text as task name
                const taskName = titleText.trim();
                const taskAction = 'Task Completion';

                currentPaymentGroup.tasks.push({
                  date: currentDate || new Date().toISOString().split('T')[0],
                  taskName: taskName,
                  taskAction: taskAction
                });

                console.log(`Row ${i}: Added standalone task "${taskName}" with action "${taskAction}" to payment group (indentation: ${indentationLevel})`);
              }
            }

            // Update payment group with the latest payment information
            if (amount) currentPaymentGroup.amount = amount;
            if (timeReported) currentPaymentGroup.timeReported = timeReported;
            if (timeAgo) currentPaymentGroup.timeAgo = timeAgo;
            if (status) currentPaymentGroup.status = status;

          } else if (titleText && titleText.trim() && currentPaymentGroup) {
            // This might be an additional task under the current date
            if (indentationLevel === 10 && currentParentTaskName) {
              // This is a child row (indentation 10) - use parent task name and current text as action
              const taskName = currentParentTaskName;
              const taskAction = titleText.trim();

              currentPaymentGroup.tasks.push({
                date: currentDate || new Date().toISOString().split('T')[0],
                taskName: taskName,
                taskAction: taskAction
              });
              console.log(`Row ${i}: Added additional task "${taskName}" with action "${taskAction}" to current group`);
            } else if (indentationLevel === 5) {
              // This is a parent row (indentation 5) - only set parent task name, don't create task entry
              console.log(`Row ${i}: Parent row "${titleText}" - not creating additional task entry (indentation: ${indentationLevel})`);
            } else {
              // This is a standalone row (indentation 0) - use current text as task name
              const taskName = titleText.trim();
              const taskAction = 'Task Entry';

              currentPaymentGroup.tasks.push({
                date: currentDate || new Date().toISOString().split('T')[0],
                taskName: taskName,
                taskAction: taskAction
              });
              console.log(`Row ${i}: Added additional standalone task "${taskName}" with action "${taskAction}" to current group`);
            }
          } else if (titleText && titleText.trim()) {
            // Standalone task without a payment group
            if (indentationLevel === 10 && currentParentTaskName) {
              // This is a child row (indentation 10) - use parent task name and current text as action
              const taskName = currentParentTaskName;
              const taskAction = titleText.trim();

              const standaloneEntry = {
                id: generateId(),
                tasks: [{
                  date: currentDate || new Date().toISOString().split('T')[0],
                  taskName: taskName,
                  taskAction: taskAction
                }],
                amount: amount,
                timeReported: timeReported,
                timeAgo: timeAgo,
                status: status,
                extractedAt: new Date().toISOString(),
                sourceUrl: window.location.href
              };
              extractedEntries.push(standaloneEntry);
              console.log(`Row ${i}: Added standalone entry for "${taskName}" with action "${taskAction}"`);
            } else if (indentationLevel === 5) {
              // This is a parent row (indentation 5) - only set parent task name, don't create standalone entry
              console.log(`Row ${i}: Parent row "${titleText}" - not creating standalone entry (indentation: ${indentationLevel})`);
            } else {
              // This is a standalone row (indentation 0) - use current text as task name
              const taskName = titleText.trim();
              const taskAction = 'Task Entry';

              const standaloneEntry = {
                id: generateId(),
                tasks: [{
                  date: currentDate || new Date().toISOString().split('T')[0],
                  taskName: taskName,
                  taskAction: taskAction
                }],
                amount: amount,
                timeReported: timeReported,
                timeAgo: timeAgo,
                status: status,
                extractedAt: new Date().toISOString(),
                sourceUrl: window.location.href
              };
              extractedEntries.push(standaloneEntry);
              console.log(`Row ${i}: Added standalone entry for "${taskName}" with action "${taskAction}"`);
            }
          }

        } catch (error) {
          console.warn(`Row ${i}: Failed to extract data:`, error);
        }
      }

      // Add the final payment group if it exists
      if (currentPaymentGroup && (currentPaymentGroup.tasks.length > 0 || currentPaymentGroup.amount)) {
        extractedEntries.push(currentPaymentGroup);
        console.log(`Added final payment group with ${currentPaymentGroup.tasks.length} tasks`);
      }

      console.log(`Extracted ${extractedEntries.length} payment entries`);
      console.log('Sample extracted entry:', extractedEntries[0]);

      return {
        entries: extractedEntries,
        extractedAt: new Date().toISOString(),
        totalEntries: extractedEntries.length,
        sourceUrl: window.location.href
      };

    } catch (error) {
      console.error('Payment data extraction failed:', error);
      throw error;
    }
  }

  // Send data to main application using clipboard
  async function sendDataToMainApp(data) {
    try {
      // Copy data to clipboard for user to paste into main app
      const clipboardData = JSON.stringify(data, null, 2);
      await navigator.clipboard.writeText(clipboardData);
      console.log('✅ Payment data copied to clipboard successfully');
      console.log('📋 Clipboard data length:', clipboardData.length);
      return { success: true, method: 'clipboard', entriesCount: data.totalEntries };
    } catch (clipboardError) {
      console.error('❌ Failed to copy to clipboard:', clipboardError);
      return { success: false, method: 'none', error: clipboardError.message };
    }
  }

  // Create and inject the export toolbar
  function createExportToolbar() {
    console.log('🔧 createExportToolbar called');
    console.log('📍 Current URL:', window.location.href);
    console.log('🌐 Document body exists:', !!document.body);

    // Check if toolbar already exists
    const existingToolbar = document.getElementById('payment-export-toolbar');
    if (existingToolbar) {
      console.log('⚠️ Toolbar already exists, skipping creation');
      return;
    }

    console.log('✨ Creating new export toolbar...');

    const toolbar = document.createElement('div');
    toolbar.id = 'payment-export-toolbar';
    toolbar.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10000;
      background: #1976d2;
      color: white;
      padding: 10px 15px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 10px;
    `;

    const button = document.createElement('button');
    button.textContent = 'Export Payment Data';
    button.style.cssText = `
      background: white;
      color: #1976d2;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
    `;

    const status = document.createElement('span');
    status.id = 'export-status';
    status.textContent = 'Ready to export';

    button.addEventListener('click', async () => {
      try {
        status.textContent = 'Extracting data...';
        button.disabled = true;

        const data = await extractPaymentData();
        const result = await sendDataToMainApp(data);

        if (result.success) {
          status.textContent = `📋 Copied ${result.entriesCount} entries to clipboard`;
          button.textContent = 'Data Copied! ✅';
        } else {
          status.textContent = `❌ Export failed: ${result.error}`;
          button.textContent = 'Export Failed ❌';
        }

        setTimeout(() => {
          status.textContent = 'Ready to export';
          button.textContent = 'Export Payment Data';
          button.disabled = false;
        }, 5000);

      } catch (error) {
        status.textContent = 'Export failed';
        button.textContent = 'Export Failed ❌';
        console.error('Export failed:', error);
        setTimeout(() => {
          status.textContent = 'Ready to export';
          button.textContent = 'Export Payment Data';
          button.disabled = false;
        }, 5000);
      }
    });

    toolbar.appendChild(button);
    toolbar.appendChild(status);
    document.body.appendChild(toolbar);

    console.log('✅ Export toolbar successfully added to DOM');
    console.log('🎯 Toolbar element:', toolbar);
    console.log('📍 Toolbar position: top-right corner');


  }

  // Initialize the extractor
  function initialize() {
    console.log('🚀 Data Annotation Payment Extractor initialized');
    console.log('📍 Current URL:', window.location.href);
    console.log('📄 Document ready state:', document.readyState);

    // Add a visual indicator that the script is loaded
    document.body.style.border = '3px solid #1976d2';
    setTimeout(() => {
      if (document.body.style.border) {
        document.body.style.border = '';
      }
    }, 2000);

    // Wait for page to be fully loaded
    if (document.readyState === 'loading') {
      console.log('⏳ Waiting for DOMContentLoaded...');
      document.addEventListener('DOMContentLoaded', () => {
        console.log('✅ DOMContentLoaded fired, creating toolbar...');
        createExportToolbar();
      });
    } else {
      console.log('✅ Document already loaded, creating toolbar immediately...');
      createExportToolbar();
    }

    // Also create toolbar when navigating (for SPA behavior)
    setTimeout(() => {
      console.log('🔄 Creating toolbar after 2s delay (SPA fallback)...');
      createExportToolbar();
    }, 2000);

    // Additional fallback for slow-loading pages
    setTimeout(() => {
      console.log('🔄 Creating toolbar after 5s delay (slow page fallback)...');
      createExportToolbar();
    }, 5000);
  }

  // Start initialization
  console.log('🎯 Starting Data Annotation Payment Extractor...');
  initialize();

})();
